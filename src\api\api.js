// ملف axios للتعامل مع API
import axios from 'axios';

// إنشاء instance من axios مع الإعدادات الأساسية
const api = axios.create({
  baseURL: 'https://jsonplaceholder.typicode.com',
  timeout: 10000, // 10 ثواني timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// دالة لجلب بيانات المستخدمين
export const fetchUsers = async () => {
  try {
    const response = await api.get('/users');
    return response.data;
  } catch (error) {
    console.error('خطأ في جلب بيانات المستخدمين:', error);
    throw error;
  }
};

// يمكن إضافة المزيد من دوال API هنا في المستقبل
export default api;
