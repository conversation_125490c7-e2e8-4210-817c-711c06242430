// صفحة المستخدمين مع DataGrid
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Alert,
  CircularProgress,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { fetchUsers } from '../api/api';

const Users = () => {
  // حالات المكون
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تعريف أعمدة الجدول
  const columns = [
    {
      field: 'id',
      headerName: 'المعرف',
      width: 90,
      type: 'number',
    },
    {
      field: 'name',
      headerName: 'الاسم',
      width: 200,
      editable: false,
    },
    {
      field: 'username',
      headerName: 'اسم المستخدم',
      width: 150,
      editable: false,
    },
    {
      field: 'email',
      headerName: 'البريد الإلكتروني',
      width: 250,
      editable: false,
    },
    {
      field: 'city',
      headerName: 'المدينة',
      width: 150,
      editable: false,
      // استخراج المدينة من كائن العنوان
      valueGetter: (params) => params.row.address?.city || 'غير محدد',
    },
    {
      field: 'phone',
      headerName: 'رقم الهاتف',
      width: 180,
      editable: false,
    },
    {
      field: 'website',
      headerName: 'الموقع الإلكتروني',
      width: 200,
      editable: false,
      renderCell: (params) => (
        <a
          href={`http://${params.value}`}
          target="_blank"
          rel="noopener noreferrer"
          style={{ color: '#1976d2', textDecoration: 'none' }}
        >
          {params.value}
        </a>
      ),
    },
  ];

  // دالة لجلب بيانات المستخدمين
  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // جلب البيانات من API
      const userData = await fetchUsers();
      setUsers(userData);
    } catch (err) {
      setError('حدث خطأ أثناء تحميل بيانات المستخدمين. يرجى المحاولة مرة أخرى.');
      console.error('خطأ في تحميل المستخدمين:', err);
    } finally {
      setLoading(false);
    }
  };

  // تحميل البيانات عند تحميل المكون
  useEffect(() => {
    loadUsers();
  }, []);

  // عرض حالة التحميل
  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '400px',
        }}
      >
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          جاري تحميل بيانات المستخدمين...
        </Typography>
      </Box>
    );
  }

  // عرض رسالة الخطأ
  if (error) {
    return (
      <Box>
        <Typography variant="h4" component="h1" gutterBottom>
          المستخدمون
        </Typography>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box>
      {/* عنوان الصفحة */}
      <Typography variant="h4" component="h1" gutterBottom>
        المستخدمون
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        قائمة بجميع المستخدمين المسجلين في النظام. البيانات محملة من API خارجي.
      </Typography>

      {/* جدول البيانات */}
      <Paper sx={{ width: '100%', mb: 2 }}>
        <DataGrid
          rows={users}
          columns={columns}
          initialState={{
            pagination: {
              paginationModel: {
                pageSize: 10,
              },
            },
          }}
          pageSizeOptions={[5, 10, 25]}
          checkboxSelection
          disableRowSelectionOnClick
          sx={{
            border: 0,
            '& .MuiDataGrid-cell:hover': {
              color: 'primary.main',
            },
          }}
          localeText={{
            // ترجمة النصوص إلى العربية
            noRowsLabel: 'لا توجد بيانات',
            noResultsOverlayLabel: 'لم يتم العثور على نتائج',
            errorOverlayDefaultLabel: 'حدث خطأ',
            toolbarColumns: 'الأعمدة',
            toolbarColumnsLabel: 'اختر الأعمدة',
            toolbarFilters: 'المرشحات',
            toolbarFiltersLabel: 'إظهار المرشحات',
            toolbarDensity: 'الكثافة',
            toolbarDensityLabel: 'كثافة الجدول',
            toolbarDensityCompact: 'مضغوط',
            toolbarDensityStandard: 'عادي',
            toolbarDensityComfortable: 'مريح',
            toolbarExport: 'تصدير',
            toolbarExportLabel: 'تصدير',
            toolbarExportCSV: 'تحميل كـ CSV',
            toolbarExportPrint: 'طباعة',
            columnsPanelTextFieldLabel: 'البحث في العمود',
            columnsPanelTextFieldPlaceholder: 'عنوان العمود',
            columnsPanelDragIconLabel: 'إعادة ترتيب العمود',
            columnsPanelShowAllButton: 'إظهار الكل',
            columnsPanelHideAllButton: 'إخفاء الكل',
            filterPanelAddFilter: 'إضافة مرشح',
            filterPanelDeleteIconLabel: 'حذف',
            filterPanelOperators: 'المشغلات',
            filterPanelOperatorAnd: 'و',
            filterPanelOperatorOr: 'أو',
            filterPanelColumns: 'الأعمدة',
            filterPanelInputLabel: 'القيمة',
            filterPanelInputPlaceholder: 'قيمة المرشح',
            filterOperatorContains: 'يحتوي على',
            filterOperatorEquals: 'يساوي',
            filterOperatorStartsWith: 'يبدأ بـ',
            filterOperatorEndsWith: 'ينتهي بـ',
            filterOperatorIs: 'هو',
            filterOperatorNot: 'ليس',
            filterOperatorAfter: 'بعد',
            filterOperatorOnOrAfter: 'في أو بعد',
            filterOperatorBefore: 'قبل',
            filterOperatorOnOrBefore: 'في أو قبل',
            filterOperatorIsEmpty: 'فارغ',
            filterOperatorIsNotEmpty: 'غير فارغ',
            columnMenuLabel: 'القائمة',
            columnMenuShowColumns: 'إظهار الأعمدة',
            columnMenuFilter: 'مرشح',
            columnMenuHideColumn: 'إخفاء',
            columnMenuUnsort: 'إلغاء الترتيب',
            columnMenuSortAsc: 'ترتيب تصاعدي',
            columnMenuSortDesc: 'ترتيب تنازلي',
            columnHeaderFiltersTooltipActive: (count) =>
              count !== 1 ? `${count} مرشحات نشطة` : `${count} مرشح نشط`,
            columnHeaderFiltersLabel: 'إظهار المرشحات',
            columnHeaderSortIconLabel: 'ترتيب',
            footerRowSelected: (count) =>
              count !== 1
                ? `${count.toLocaleString()} صفوف محددة`
                : `${count.toLocaleString()} صف محدد`,
            footerTotalRows: 'إجمالي الصفوف:',
            footerTotalVisibleRows: (visibleCount, totalCount) =>
              `${visibleCount.toLocaleString()} من ${totalCount.toLocaleString()}`,
          }}
        />
      </Paper>

      {/* معلومات إضافية */}
      <Typography variant="body2" color="text.secondary">
        إجمالي المستخدمين: {users.length} مستخدم
      </Typography>
    </Box>
  );
};

export default Users;
