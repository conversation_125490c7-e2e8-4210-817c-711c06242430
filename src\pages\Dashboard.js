// صفحة لوحة التحكم الرئيسية
import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Paper,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';

// مكون بطاقة إحصائية
const StatCard = ({ title, value, icon, color }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            p: 1,
            mr: 2,
            color: 'white',
          }}
        >
          {icon}
        </Box>
        <Typography variant="h6" component="div">
          {title}
        </Typography>
      </Box>
      <Typography variant="h4" component="div" color="primary">
        {value}
      </Typography>
    </CardContent>
  </Card>
);

const Dashboard = () => {
  // بيانات وهمية للإحصائيات
  const stats = [
    {
      title: 'إجمالي المستخدمين',
      value: '10',
      icon: <PeopleIcon />,
      color: '#1976d2',
    },
    {
      title: 'المستخدمين النشطين',
      value: '8',
      icon: <TrendingUpIcon />,
      color: '#2e7d32',
    },
    {
      title: 'التقارير',
      value: '25',
      icon: <AssessmentIcon />,
      color: '#ed6c02',
    },
    {
      title: 'المهام المكتملة',
      value: '15',
      icon: <DashboardIcon />,
      color: '#9c27b0',
    },
  ];

  return (
    <Box>
      {/* عنوان الصفحة */}
      <Typography variant="h4" component="h1" gutterBottom>
        لوحة التحكم
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        مرحباً بك في نظام إدارة المستخدمين. هنا يمكنك مراقبة الإحصائيات العامة للنظام.
      </Typography>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard
              title={stat.title}
              value={stat.value}
              icon={stat.icon}
              color={stat.color}
            />
          </Grid>
        ))}
      </Grid>

      {/* قسم المعلومات الإضافية */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              نظرة عامة على النظام
            </Typography>
            <Typography variant="body1" paragraph>
              هذا النظام يوفر واجهة بسيطة وسهلة الاستخدام لإدارة المستخدمين. 
              يمكنك التنقل بين الصفحات باستخدام القائمة الجانبية.
            </Typography>
            <Typography variant="body1" paragraph>
              الميزات الرئيسية:
            </Typography>
            <ul>
              <li>عرض قائمة المستخدمين مع إمكانية البحث والفلترة</li>
              <li>واجهة مستخدم حديثة باستخدام Material UI</li>
              <li>تصميم متجاوب يعمل على جميع الأجهزة</li>
              <li>تحميل البيانات من API خارجي</li>
            </ul>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              الإجراءات السريعة
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • عرض المستخدمين
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • إضافة مستخدم جديد
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • تحديث البيانات
            </Typography>
            <Typography variant="body2" color="text.secondary">
              • إنشاء تقرير
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
