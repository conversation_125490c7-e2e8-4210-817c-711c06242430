// المكون الرئيسي للتطبيق - يحتوي على React Router والتنقل
import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

// استيراد المكونات
import Layout from './components/Layout';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';

// إنشاء theme مخصص للتطبيق
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
  typography: {
    fontFamily: [
      'Roboto',
      'Arial',
      'sans-serif',
    ].join(','),
  },
  direction: 'rtl', // دعم اللغة العربية
});

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Layout>
          <Routes>
            {/* الصفحة الرئيسية - Dashboard */}
            <Route path="/" element={<Dashboard />} />

            {/* صفحة المستخدمين */}
            <Route path="/users" element={<Users />} />

            {/* يمكن إضافة المزيد من الصفحات هنا */}
          </Routes>
        </Layout>
      </Router>
    </ThemeProvider>
  );
}

export default App;
